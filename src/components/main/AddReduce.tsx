import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { theme } from "../../theme";
import { RoundButton } from "./buttons/RoundButton";
import { Ionicons } from "@expo/vector-icons";
import { Row } from "./Row";

interface AddReduceProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  icon?: React.ReactNode;
  padding?: number;
}

export const AddReduce: React.FC<AddReduceProps> = ({
  value,
  onChange,
  min = 1,
  max = Infinity,
  icon,
  padding = theme.spacing.xs,
}) => {
  const handleReduce = () => {
    const newValue = Math.max(min, value - 1);
    onChange(newValue);
  };

  const handleAdd = () => {
    const newValue = Math.min(max, value + 1);
    onChange(newValue);
  };

  return (
    <Row align="center" justify="space-between">
      <RoundButton
        icon={
          <Ionicons
            name="remove-outline"
            size={theme.fontSizes.md}
            color={theme.colors.white}
          />
        }
        onPress={handleReduce}
      />
      <View>
        <Row
          style={{
            flex: 1,
            alignItems: "center",
          }}
        >
          {icon && <>{icon}</>}
          <View style={{ marginLeft: theme.spacing.sm }}>
            <Text style={styles.value}>{value}</Text>
          </View>
        </Row>
      </View>
      <RoundButton
        icon={
          <Ionicons
            name="add-outline"
            size={theme.fontSizes.md}
            color={theme.colors.white}
          />
        }
        onPress={handleAdd}
      />
    </Row>
  );
};

const styles = StyleSheet.create({
  value: {
    fontSize: theme.fontSizes.md,
    textAlign: "center",
    color: theme.colors.gray700,
  },
});
