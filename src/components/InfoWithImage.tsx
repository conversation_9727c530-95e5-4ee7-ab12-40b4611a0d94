import React from "react";
import {
  ImageBackground,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../theme";
import { Pill } from "./Pill";

interface InfoWithImageProps {
  title: string;
  description: string;
  imageUrl: string;
  pillText?: string;
  onPress?: () => void;
  showReadMore?: boolean;
}

export const InfoWithImage: React.FC<InfoWithImageProps> = ({
  title,
  description,
  imageUrl,
  pillText,
  onPress,
  showReadMore = false,
}) => {
  const Component = onPress ? TouchableOpacity : View;

  return (
    <Component
      style={styles.container}
      onPress={onPress}
      activeOpacity={onPress ? 0.9 : 1}
    >
      <View style={styles.imageContainer}>
        <ImageBackground
          source={{
            uri: imageUrl,
          }}
          style={styles.image}
          imageStyle={styles.imageStyle}
        >
          {/* Overlay for better text readability */}
          <View style={styles.imageOverlay} />

          {/* Pill overlay on image */}
          {pillText && (
            <View style={styles.pillContainer}>
              <Pill label={pillText} />
            </View>
          )}

          {/* Read more indicator */}
          {showReadMore && onPress && (
            <View style={styles.readMoreIndicator}>
              <MaterialCommunityIcons
                name="arrow-expand"
                size={24}
                color={theme.colors.white}
              />
            </View>
          )}
        </ImageBackground>
      </View>

      <View style={styles.contentContainer}>
        <Text style={styles.title} numberOfLines={2}>
          {title}
        </Text>

        <Text style={styles.description} numberOfLines={4}>
          {description}
        </Text>

        {showReadMore && onPress && (
          <View style={styles.readMoreContainer}>
            <Text style={styles.readMoreText}>Leer más</Text>
            <MaterialCommunityIcons
              name="chevron-right"
              size={16}
              color={theme.colors.primary}
            />
          </View>
        )}
      </View>
    </Component>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.radii.lg,
    overflow: "hidden",
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  imageContainer: {
    position: "relative",
  },
  image: {
    width: "100%",
    height: 240,
    justifyContent: "space-between",
  },
  imageStyle: {
    borderTopLeftRadius: theme.radii.lg,
    borderTopRightRadius: theme.radii.lg,
  },
  imageOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0, 0, 0, 0.2)",
  },
  pillContainer: {
    position: "absolute",
    top: theme.spacing.md,
    left: theme.spacing.md,
    zIndex: 2,
  },
  readMoreIndicator: {
    position: "absolute",
    top: theme.spacing.md,
    right: theme.spacing.md,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: theme.radii.xl,
    padding: theme.spacing.sm,
    zIndex: 2,
  },
  contentContainer: {
    padding: theme.spacing.lg,
  },
  title: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: theme.spacing.sm,
    lineHeight: 28,
  },
  description: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray700,
    lineHeight: 22,
    marginBottom: theme.spacing.md,
  },
  readMoreContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    marginTop: theme.spacing.sm,
  },
  readMoreText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.primary,
    marginRight: 4,
  },
});
