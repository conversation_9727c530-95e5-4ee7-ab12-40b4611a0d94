import { StyleSheet, Text, TouchableOpacity } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { Complaint } from "../../interfaces/complaint";
import { Status } from "../../interfaces/maintenance-issue-report";
import { Card, Col, Row, Section } from "../main";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { PROPERTY_SCREENS } from "../../navigation/constants";
import { PartialProperty } from "../../interfaces/me";
import { PropertyStackNavigationProp } from "../../navigation/types";

interface ComplaintsSectionProps {
  complaints: Complaint[];
  property: PartialProperty;
}

export const ComplaintsSection: React.FC<ComplaintsSectionProps> = ({
  complaints,
  property,
}) => {
  const navigation = useNavigation<PropertyStackNavigationProp>();

  if (!complaints.length) {
    return (
      <Section title="Quejas">
        <Text style={styles.noDataText}>No hay quejas registradas</Text>
      </Section>
    );
  }

  // Separar por estado
  const openComplaints = complaints.filter((c) => c.status === Status.OPEN);
  const inProgressComplaints = complaints.filter(
    (c) => c.status === Status.IN_PROGRESS
  );
  const resolvedComplaints = complaints.filter(
    (c) => c.status === Status.RESOLVED
  );

  return (
    <Section title="Quejas">
      <Card style={styles.summaryCard}>
        <Row align="center" style={styles.summaryRow}>
          <Col justify="center" align="center">
            <MaterialCommunityIcons
              name="clock-outline"
              size={24}
              color={theme.colors.primary}
            />
            <Text style={styles.summaryNumber}>{openComplaints.length}</Text>
            <Text style={styles.summaryLabel}>Abiertas</Text>
          </Col>

          <Col justify="center" align="center">
            <MaterialCommunityIcons
              name="clock-fast"
              size={24}
              color={theme.colors.warning}
            />
            <Text style={styles.summaryNumber}>
              {inProgressComplaints.length}
            </Text>
            <Text style={styles.summaryLabel}>En Progreso</Text>
          </Col>

          <Col justify="center" align="center">
            <MaterialCommunityIcons
              name="check-circle"
              size={24}
              color={theme.colors.success}
            />
            <Text style={styles.summaryNumber}>
              {resolvedComplaints.length}
            </Text>
            <Text style={styles.summaryLabel}>Resueltas</Text>
          </Col>
        </Row>

        <TouchableOpacity
          style={styles.viewAllRow}
          onPress={() =>
            navigation.navigate(PROPERTY_SCREENS.PROPERTY_COMPLAINTS, {
              complaints,
              property,
            })
          }
          activeOpacity={0.7}
        >
          <Row align="center" style={styles.viewAllRow}>
            <Text style={styles.viewAllText}>Ver todas las quejas</Text>
            <MaterialCommunityIcons
              name="chevron-right"
              size={16}
              color={theme.colors.primary}
            />
          </Row>
        </TouchableOpacity>
      </Card>
    </Section>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
    marginBottom: theme.spacing.md,
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
    paddingVertical: theme.spacing.sm,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  viewAllRow: {
    justifyContent: "center",
    paddingTop: theme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
  viewAllText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.primary,
    fontWeight: "600",
    marginRight: theme.spacing.xs,
  },

  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
