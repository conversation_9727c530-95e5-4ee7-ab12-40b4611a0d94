import React from "react";
import { Text, StyleSheet, TouchableOpacity, View } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { MonthlyMaintenanceCharge } from "../../interfaces/monthly-maintenance-charge";
import { Card } from "../main/Card";
import { Row } from "../main/Row";
import { Col } from "../main/Col";
import { Section } from "../main/Section";
import { PROPERTY_SCREENS } from "../../navigation/constants";
import { PropertyStackNavigationProp } from "../../navigation/types";

interface MonthlyChargesSectionProps {
  charges: MonthlyMaintenanceCharge[];
}

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("es-MX", {
    style: "currency",
    currency: "MXN",
  }).format(amount);
};

const isChargeOverdue = (dueDate: string): boolean => {
  return new Date(dueDate) < new Date();
};

export const MonthlyChargesSection: React.FC<MonthlyChargesSectionProps> = ({
  charges,
}) => {
  const navigation = useNavigation<PropertyStackNavigationProp>();

  if (!charges.length) {
    return (
      <Section title="Cargos mensuales">
        <Text style={styles.noDataText}>
          No hay cargos mensuales registrados
        </Text>
      </Section>
    );
  }

  // Separar por estado de pago
  const unpaidCharges = charges.filter((charge) => !charge.isPaid);
  const paidCharges = charges.filter((charge) => charge.isPaid);
  const overdueCharges = unpaidCharges.filter((charge) =>
    isChargeOverdue(charge.dueDate)
  );

  // Calcular total adeudado
  const totalOwed = unpaidCharges.reduce((sum, charge) => {
    const amount = charge.maintenanceFee?.amount ?? 0;
    const lateFee = charge.lateFeeApplied ? charge.lateFeeAmount ?? 0 : 0;
    return sum + amount + lateFee;
  }, 0);

  return (
    <Section title="Cargos mensuales">
      <Col>
        {/* Resumen de cargos */}

        <Card style={styles.summaryCard}>
          <Row align="center" style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <MaterialCommunityIcons
                name="clock-outline"
                size={24}
                color={theme.colors.warning}
              />
              <Text style={styles.summaryNumber}>{unpaidCharges.length}</Text>
              <Text style={styles.summaryLabel}>Pendientes</Text>
            </View>

            <View style={styles.summaryItem}>
              <MaterialCommunityIcons
                name="check-circle"
                size={24}
                color={theme.colors.success}
              />
              <Text style={styles.summaryNumber}>{paidCharges.length}</Text>
              <Text style={styles.summaryLabel}>Pagados</Text>
            </View>

            <View style={styles.summaryItem}>
              <MaterialCommunityIcons
                name="alert-circle"
                size={24}
                color={theme.colors.error}
              />
              <Text style={styles.summaryNumber}>{overdueCharges.length}</Text>
              <Text style={styles.summaryLabel}>Vencidos</Text>
            </View>
          </Row>

          {/* Información de adeudos */}
          {totalOwed > 0 && (
            <View style={styles.debtContainer}>
              <Text style={styles.debtLabel}>Total adeudado:</Text>
              <Text style={styles.debtAmount}>{formatCurrency(totalOwed)}</Text>
            </View>
          )}
          <TouchableOpacity
            onPress={() =>
              navigation.navigate(PROPERTY_SCREENS.PROPERTY_MONTHLY_CHARGES, {})
            }
            activeOpacity={0.7}
          >
            <Row align="center" style={styles.viewAllRow}>
              <Text style={styles.viewAllText}>Ver todos los cargos</Text>
              <MaterialCommunityIcons
                name="chevron-right"
                size={16}
                color={theme.colors.primary}
              />
            </Row>
          </TouchableOpacity>
        </Card>
      </Col>
    </Section>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
    marginBottom: theme.spacing.md,
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
    paddingVertical: theme.spacing.sm,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  debtContainer: {
    backgroundColor: `${theme.colors.warning}10`,
    borderRadius: theme.radii.md,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.warning,
    alignItems: "center",
  },
  debtLabel: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.warning,
    fontWeight: "600",
  },
  debtAmount: {
    fontSize: theme.fontSizes.xl,
    color: theme.colors.warning,
    fontWeight: "700",
    marginTop: 2,
  },
  viewAllRow: {
    justifyContent: "center",
    paddingTop: theme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
  viewAllText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.primary,
    fontWeight: "600",
    marginRight: theme.spacing.xs,
  },
  recentChargesContainer: {
    marginTop: theme.spacing.sm,
  },
  recentChargesTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: theme.spacing.sm,
  },
  chargeCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  overdueCard: {
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.error,
    backgroundColor: `${theme.colors.error}05`,
  },
  chargeIndicator: {
    width: 40,
    height: 40,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
    backgroundColor: `${theme.colors.gray200}50`,
  },
  chargeInfo: {
    flex: 1,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.xs,
  },
  chargeAmount: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
  },
  overdueBadge: {
    backgroundColor: theme.colors.error,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  overdueText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
  },
  chargePeriod: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.primary,
    fontWeight: "600",
    marginBottom: theme.spacing.xs,
  },
  chargeDueDate: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginBottom: theme.spacing.xs,
  },
  overdueDueDate: {
    color: theme.colors.error,
    fontWeight: "600",
  },
  lateFeeText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.warning,
    fontStyle: "italic",
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});

// Export utility functions for use in other components
export { formatCurrency, isChargeOverdue };
