import React from "react";
import { Text, StyleSheet } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { Pet } from "../../interfaces/pet";
import { Card } from "../main/Card";
import { Row } from "../main/Row";
import { Col } from "../main/Col";
import { Section } from "../main/Section";
import { getPetIcon } from "../../utils/convertions";

interface PetsSectionProps {
  pets: Pet[];
}

export const PetsSection: React.FC<PetsSectionProps> = ({ pets }) => {
  if (!pets.length) {
    return (
      <Section title="Mascotas">
        <Text style={styles.noDataText}>No hay mascotas registradas</Text>
      </Section>
    );
  }

  return (
    <Section title="Mascotas">
      <Col>
        {pets.map((pet) => (
          <Card key={pet.id} style={styles.petCard}>
            <Row align="center">
              <MaterialCommunityIcons
                name={getPetIcon(pet.type)}
                size={theme.fontSizes.xl}
                color={theme.colors.primary}
                style={styles.petIcon}
              />
              <Col style={styles.petInfo}>
                <Text style={styles.petName}>{pet.name}</Text>
                <Text style={styles.petType}>{pet.type}</Text>
              </Col>
            </Row>
          </Card>
        ))}
      </Col>
    </Section>
  );
};

const styles = StyleSheet.create({
  petCard: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: theme.spacing.sm,
  },
  petIcon: {
    marginRight: theme.spacing.md,
  },
  petInfo: {
    flex: 1,
  },
  petName: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: 2,
  },
  petType: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    textTransform: "capitalize",
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
