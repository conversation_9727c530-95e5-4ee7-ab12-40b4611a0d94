import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { PartialProperty } from "../../interfaces/me";
import { PropertyType } from "../../interfaces/property";
import { Card } from "../main/Card";
import { Row } from "../main/Row";
import { Col } from "../main/Col";
import { Section } from "../main/Section";

interface PropertyInfoSectionProps {
  property: PartialProperty;
}

const getPropertyTypeLabel = (type: PropertyType): string => {
  switch (type) {
    case PropertyType.HOUSE:
      return "Casa";
    case PropertyType.DEPARTMENT:
      return "Departamento";
    default:
      return "Propiedad";
  }
};

const getPropertyIcon = (
  type: PropertyType
): keyof typeof MaterialCommunityIcons.glyphMap => {
  switch (type) {
    case PropertyType.HOUSE:
      return "home";
    case PropertyType.DEPARTMENT:
      return "office-building";
    default:
      return "map-marker";
  }
};

export const PropertyInfoSection: React.FC<PropertyInfoSectionProps> = ({
  property,
}) => {
  return (
    <Section title="Información de la propiedad">
      <Card style={styles.card}>
        <Col>
          {/* Tipo de propiedad */}
          <Row align="center" style={styles.infoRow}>
            <MaterialCommunityIcons
              name={getPropertyIcon(property.type)}
              size={theme.fontSizes.lg}
              color={theme.colors.primary}
            />
            <Col style={styles.textContainer}>
              <Text style={styles.label}>Tipo</Text>
              <Text style={styles.value}>
                {getPropertyTypeLabel(property.type)}
              </Text>
            </Col>
          </Row>

          {/* Dirección */}
          <Row align="center" style={styles.infoRow}>
            <MaterialCommunityIcons
              name="map-marker"
              size={theme.fontSizes.lg}
              color={theme.colors.primary}
            />
            <Col style={styles.textContainer}>
              <Text style={styles.label}>Dirección</Text>
              <Text style={styles.value}>{property.address}</Text>
            </Col>
          </Row>

          {/* Estadísticas rápidas */}
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <MaterialCommunityIcons
                name="account-group"
                size={theme.fontSizes.md}
                color={theme.colors.primary}
              />
              <Text style={styles.statNumber}>
                {property.residents?.length || 0}
              </Text>
              <Text style={styles.statLabel}>Residentes</Text>
            </View>

            <View style={styles.statItem}>
              <MaterialCommunityIcons
                name="car"
                size={theme.fontSizes.md}
                color={theme.colors.primary}
              />
              <Text style={styles.statNumber}>
                {property.vehicles?.length || 0}
              </Text>
              <Text style={styles.statLabel}>Vehículos</Text>
            </View>

            <View style={styles.statItem}>
              <MaterialCommunityIcons
                name="paw"
                size={theme.fontSizes.md}
                color={theme.colors.primary}
              />
              <Text style={styles.statNumber}>
                {property.pets?.length || 0}
              </Text>
              <Text style={styles.statLabel}>Mascotas</Text>
            </View>

            <View style={styles.statItem}>
              <MaterialCommunityIcons
                name="parking"
                size={theme.fontSizes.md}
                color={theme.colors.primary}
              />
              <Text style={styles.statNumber}>
                {property.parkingSpots?.length || 0}
              </Text>
              <Text style={styles.statLabel}>Estacionamientos</Text>
            </View>
          </View>
        </Col>
      </Card>
    </Section>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: "column",
  },
  infoRow: {
    marginBottom: theme.spacing.md,
  },
  textContainer: {
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  label: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    fontWeight: "500",
  },
  value: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.black,
    fontWeight: "600",
    marginTop: 2,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginTop: theme.spacing.md,
    paddingTop: theme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statNumber: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.primary,
    marginTop: theme.spacing.xs,
  },
  statLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
});
