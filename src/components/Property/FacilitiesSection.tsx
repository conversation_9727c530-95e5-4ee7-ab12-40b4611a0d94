import React from "react";
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { Facility } from "../../interfaces/facility";
import { Card } from "../main/Card";
import { Row } from "../main/Row";
import { Col } from "../main/Col";
import { Section } from "../main/Section";
import { getStringTime } from "../../utils/date-time.utils";

interface FacilitiesSectionProps {
  facilities: Facility[];
  onFacilityPress?: (facility: Facility) => void;
  onRegulationsPress?: (facility: Facility) => void;
}

const getFacilityIcon = (
  facilityName: string
): keyof typeof MaterialCommunityIcons.glyphMap => {
  const name = facilityName.toLowerCase();
  if (
    name.includes("alberca") ||
    name.includes("piscina") ||
    name.includes("pool")
  ) {
    return "pool";
  } else if (name.includes("gimnasio") || name.includes("gym")) {
    return "dumbbell";
  } else if (
    name.includes("salon") ||
    name.includes("salón") ||
    name.includes("eventos")
  ) {
    return "party-popper";
  } else if (name.includes("cancha") || name.includes("tenis")) {
    return "tennis";
  } else if (
    name.includes("futbol") ||
    name.includes("fútbol") ||
    name.includes("soccer")
  ) {
    return "soccer";
  } else if (name.includes("basquet") || name.includes("basketball")) {
    return "basketball";
  } else if (
    name.includes("jardin") ||
    name.includes("jardín") ||
    name.includes("parque")
  ) {
    return "tree";
  } else if (name.includes("terraza") || name.includes("roof")) {
    return "home-roof";
  } else {
    return "map-marker";
  }
};

const getFacilityStatusColor = (facility: Facility): string => {
  const now = new Date();
  const currentTime = now.getHours() * 60 + now.getMinutes();

  // Parse open and close times
  const openTime = facility.open
    ? parseInt(facility.open.split(":")[0]) * 60 +
      parseInt(facility.open.split(":")[1])
    : 0;
  const closeTime = facility.close
    ? parseInt(facility.close.split(":")[0]) * 60 +
      parseInt(facility.close.split(":")[1])
    : 1440;

  if (currentTime >= openTime && currentTime <= closeTime) {
    return theme.colors.success;
  } else {
    return theme.colors.error;
  }
};

const getFacilityStatus = (facility: Facility): string => {
  const now = new Date();
  const currentTime = now.getHours() * 60 + now.getMinutes();

  const openTime = facility.open
    ? parseInt(facility.open.split(":")[0]) * 60 +
      parseInt(facility.open.split(":")[1])
    : 0;
  const closeTime = facility.close
    ? parseInt(facility.close.split(":")[0]) * 60 +
      parseInt(facility.close.split(":")[1])
    : 1440;

  if (currentTime >= openTime && currentTime <= closeTime) {
    return "Abierto";
  } else {
    return "Cerrado";
  }
};

export const FacilitiesSection: React.FC<FacilitiesSectionProps> = ({
  facilities,
  onFacilityPress,
  onRegulationsPress,
}) => {
  if (!facilities.length) {
    return (
      <Section title="Instalaciones">
        <Text style={styles.noDataText}>No hay instalaciones disponibles</Text>
      </Section>
    );
  }

  return (
    <Section title="Instalaciones">
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}
      >
        {facilities.map((facility, index) => (
          <TouchableOpacity
            key={facility.id}
            onPress={() => onFacilityPress?.(facility)}
            activeOpacity={0.9}
            style={[
              styles.facilityCard,
              index === 0 && styles.firstCard,
              index === facilities.length - 1 && styles.lastCard,
            ]}
          >
            <Card style={styles.card}>
              <View style={styles.facilityHeader}>
                <View
                  style={[
                    styles.facilityIconContainer,
                    { backgroundColor: `${theme.colors.primary}20` },
                  ]}
                >
                  <MaterialCommunityIcons
                    name={getFacilityIcon(facility.name)}
                    size={32}
                    color={theme.colors.primary}
                  />
                </View>
                <View
                  style={[
                    styles.statusBadge,
                    { backgroundColor: getFacilityStatusColor(facility) },
                  ]}
                >
                  <Text style={styles.statusText}>
                    {getFacilityStatus(facility)}
                  </Text>
                </View>
              </View>

              <Col style={styles.facilityContent}>
                <Text style={styles.facilityName} numberOfLines={2}>
                  {facility.name}
                </Text>

                <Text style={styles.facilityDescription} numberOfLines={3}>
                  {facility.description}
                </Text>

                <View style={styles.facilityDetails}>
                  <Row align="center" style={styles.detailRow}>
                    <MaterialCommunityIcons
                      name="clock"
                      size={14}
                      color={theme.colors.gray500}
                    />
                    <Text style={styles.detailText}>
                      {getStringTime(facility.open)} -{" "}
                      {getStringTime(facility.close)}
                    </Text>
                  </Row>

                  {facility.maxAmountOfPeople && (
                    <Row align="center" style={styles.detailRow}>
                      <MaterialCommunityIcons
                        name="account-multiple"
                        size={14}
                        color={theme.colors.gray500}
                      />
                      <Text style={styles.detailText}>
                        Máx. {facility.maxAmountOfPeople} personas
                      </Text>
                    </Row>
                  )}

                  {facility.reservable && (
                    <Row align="center" style={styles.detailRow}>
                      <MaterialCommunityIcons
                        name="calendar-check"
                        size={14}
                        color={theme.colors.success}
                      />
                      <Text
                        style={[
                          styles.detailText,
                          { color: theme.colors.success },
                        ]}
                      >
                        Reservable
                      </Text>
                    </Row>
                  )}
                </View>

                <View style={styles.facilityActions}>
                  {facility.regulations && facility.regulations.length > 0 && (
                    <TouchableOpacity
                      style={styles.regulationsButton}
                      onPress={() => onRegulationsPress?.(facility)}
                      activeOpacity={0.7}
                    >
                      <MaterialCommunityIcons
                        name="book-open-variant"
                        size={16}
                        color={theme.colors.primary}
                      />
                      <Text style={styles.regulationsButtonText}>
                        Reglamento
                      </Text>
                    </TouchableOpacity>
                  )}

                  <TouchableOpacity
                    style={styles.viewButton}
                    onPress={() => onFacilityPress?.(facility)}
                    activeOpacity={0.7}
                  >
                    <Text style={styles.viewButtonText}>Ver detalles</Text>
                    <MaterialCommunityIcons
                      name="chevron-right"
                      size={16}
                      color={theme.colors.white}
                    />
                  </TouchableOpacity>
                </View>
              </Col>
            </Card>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </Section>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    paddingHorizontal: theme.spacing.sm,
  },
  facilityCard: {
    marginRight: theme.spacing.md,
  },
  firstCard: {
    marginLeft: 0,
  },
  lastCard: {
    marginRight: theme.spacing.lg,
  },
  card: {
    width: 280,
    height: 320,
    padding: theme.spacing.lg,
  },
  facilityHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: theme.spacing.md,
  },
  facilityIconContainer: {
    width: 60,
    height: 60,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    textTransform: "uppercase",
  },
  facilityContent: {
    flex: 1,
    justifyContent: "space-between",
  },
  facilityName: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: theme.spacing.sm,
    lineHeight: 24,
  },
  facilityDescription: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    lineHeight: 18,
    marginBottom: theme.spacing.md,
  },
  facilityDetails: {
    marginBottom: theme.spacing.md,
  },
  detailRow: {
    marginBottom: theme.spacing.xs,
  },
  detailText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  facilityActions: {
    marginTop: "auto",
  },
  regulationsButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: `${theme.colors.primary}15`,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.radii.md,
    marginBottom: theme.spacing.sm,
  },
  regulationsButtonText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.primary,
    marginLeft: 6,
  },
  viewButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.radii.md,
  },
  viewButtonText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.white,
    marginRight: 6,
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
