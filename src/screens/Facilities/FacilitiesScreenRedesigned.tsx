import React, { useState, useMemo } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Image,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { GradientView } from "../../components/layouts/GradientView";
import { Card } from "../../components/main/Card";

import { theme } from "../../theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Facility } from "../../interfaces/facility";
import { Loading } from "../../components/Loading";
import { QUERIES } from "../../constants/queries";
import { FacilitiesStackParamList } from "../../navigation/types";
import { FACILITIES_SCREENS } from "../../navigation/constants";
import { getStringTime, shortDaysOfWeek } from "../../utils/date-time.utils";

const getAvailabilityStatus = (facility: Facility) => {
  const now = new Date();
  const currentDay = now.getDay();
  const currentTime = now.getHours() * 100 + now.getMinutes();

  // Verificar si está abierto hoy
  const isOpenToday = facility.daysOfWeek.includes(currentDay);

  if (!isOpenToday) {
    return {
      status: "closed",
      label: "Cerrado hoy",
      color: theme.colors.error,
    };
  }

  // Verificar horario
  const openTime = parseInt(facility.open.replace(":", ""));
  const closeTime = parseInt(facility.close.replace(":", ""));

  if (currentTime >= openTime && currentTime <= closeTime) {
    return {
      status: "open",
      label: "Abierto ahora",
      color: theme.colors.success,
    };
  } else {
    return { status: "closed", label: "Cerrado", color: theme.colors.error };
  }
};

export const FacilitiesScreenRedesigned: React.FC = () => {
  const { data, isLoading } = useCachedQuery<Facility[]>(QUERIES.FACILITIES);
  const navigation =
    useNavigation<NativeStackNavigationProp<FacilitiesStackParamList>>();

  const [selectedFilter, setSelectedFilter] = useState<
    "all" | "reservable" | "open"
  >("all");

  const facilities = data ?? [];

  // Filtrar amenidades
  const filteredFacilities = useMemo(() => {
    let filtered = facilities;

    // Filtro por tipo
    if (selectedFilter === "reservable") {
      filtered = filtered.filter((facility) => facility.reservable);
    } else if (selectedFilter === "open") {
      filtered = filtered.filter((facility) => {
        const status = getAvailabilityStatus(facility);
        return status.status === "open";
      });
    }

    return filtered;
  }, [facilities, selectedFilter]);

  if (isLoading) {
    return <Loading />;
  }

  const reservableFacilities = facilities.filter((f) => f.reservable);
  const openFacilities = facilities.filter(
    (f) => getAvailabilityStatus(f).status === "open"
  );

  return (
    <GradientView
      firstLineText="Amenidades"
      secondLineText="Descubre y reserva"
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Estadísticas rápidas */}
        <Card style={styles.statsCard}>
          <View style={styles.statsRow}>
            <TouchableOpacity
              style={styles.statItem}
              activeOpacity={0.7}
              onPress={() => setSelectedFilter("all")}
            >
              <MaterialCommunityIcons
                name="home-city"
                size={24}
                color={theme.colors.primary}
              />
              <Text style={styles.statNumber}>{facilities.length}</Text>
              <Text style={styles.statLabel}>Total</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.statItem}
              activeOpacity={0.7}
              onPress={() => setSelectedFilter("reservable")}
            >
              <MaterialCommunityIcons
                name="calendar-check"
                size={24}
                color={theme.colors.success}
              />
              <Text style={styles.statNumber}>
                {reservableFacilities.length}
              </Text>
              <Text style={styles.statLabel}>Reservables</Text>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={() => setSelectedFilter("open")}
              style={styles.statItem}
            >
              <MaterialCommunityIcons
                name="clock-check"
                size={24}
                color={theme.colors.warning}
              />
              <Text style={styles.statNumber}>{openFacilities.length}</Text>
              <Text style={styles.statLabel}>Abiertas</Text>
            </TouchableOpacity>
          </View>
        </Card>

        {/* Lista de amenidades */}
        {filteredFacilities.length === 0 ? (
          <Card style={styles.noDataCard}>
            <MaterialCommunityIcons
              name="home-search"
              size={48}
              color={theme.colors.gray500}
            />
            <Text style={styles.noDataText}>No hay amenidades disponibles</Text>
          </Card>
        ) : (
          filteredFacilities.map((facility) => {
            const availabilityStatus = getAvailabilityStatus(facility);

            return (
              <TouchableOpacity
                key={facility.id}
                onPress={() => {
                  navigation.navigate(FACILITIES_SCREENS.FACILITY_DETAIL, {
                    facility,
                  });
                }}
                activeOpacity={0.8}
              >
                <Card style={styles.facilityCard}>
                  <View style={styles.facilityRow}>
                    {/* Imagen con overlay de icono */}
                    <View style={styles.imageContainer}>
                      <Image
                        source={{ uri: facility.imagePath }}
                        style={styles.facilityImage}
                        defaultSource={require("../../assets/icon.png")}
                      />
                    </View>

                    {/* Información de la amenidad */}
                    <View style={styles.facilityInfo}>
                      <View style={styles.headerRow}>
                        <Text style={styles.facilityName} numberOfLines={1}>
                          {facility.name}
                        </Text>
                        <View
                          style={[
                            styles.statusBadge,
                            { backgroundColor: availabilityStatus.color },
                          ]}
                        >
                          <Text style={styles.statusText}>
                            {availabilityStatus.label}
                          </Text>
                        </View>
                      </View>

                      {/* Horarios */}
                      <View style={styles.scheduleRow}>
                        <MaterialCommunityIcons
                          name="clock-outline"
                          size={14}
                          color={theme.colors.gray500}
                        />
                        <Text style={styles.scheduleText}>
                          {getStringTime(facility.open)} -{" "}
                          {getStringTime(facility.close)}
                        </Text>
                      </View>

                      {/* Días disponibles */}
                      <View style={styles.daysRow}>
                        <MaterialCommunityIcons
                          name="calendar-outline"
                          size={14}
                          color={theme.colors.gray500}
                        />
                        <Text style={styles.daysText}>
                          {facility.daysOfWeek.length
                            ? facility.daysOfWeek
                                .map((day) => shortDaysOfWeek[day])
                                .join(", ")
                            : "Sin horario definido"}
                        </Text>
                      </View>

                      {/* Información adicional */}
                      <View style={styles.extraInfoRow}>
                        {facility.reservable && (
                          <View style={styles.featureTag}>
                            <MaterialCommunityIcons
                              name="calendar-check"
                              size={12}
                              color={theme.colors.success}
                            />
                            <Text style={styles.featureText}>Reservable</Text>
                          </View>
                        )}
                        {facility.maxAmountOfPeople && (
                          <View style={styles.featureTag}>
                            <MaterialCommunityIcons
                              name="account-group"
                              size={12}
                              color={theme.colors.primary}
                            />
                            <Text style={styles.featureText}>
                              Máx. {facility.maxAmountOfPeople}
                            </Text>
                          </View>
                        )}
                      </View>
                    </View>

                    <MaterialCommunityIcons
                      name="chevron-right"
                      size={theme.fontSizes.lg}
                      color={theme.colors.gray500}
                    />
                  </View>
                </Card>
              </TouchableOpacity>
            );
          })
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  statsCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  statsRow: {
    flexDirection: "row",
    justifyContent: "center",
    flex: 1,
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  statLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  searchCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: theme.colors.gray100,
    borderRadius: theme.radii.lg,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },
  searchIcon: {
    marginRight: theme.spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: theme.fontSizes.md,
    color: theme.colors.black,
    paddingVertical: 0,
  },
  filtersCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  filtersTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: theme.spacing.md,
  },
  filtersRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginHorizontal: -theme.spacing.xs,
  },
  filterChip: {
    backgroundColor: theme.colors.gray100,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.radii.lg,
    borderWidth: 1,
    borderColor: theme.colors.gray200,
    marginHorizontal: theme.spacing.xs,
    marginBottom: theme.spacing.sm,
  },
  activeFilterChip: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  filterText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray700,
    fontWeight: "500",
  },
  activeFilterText: {
    color: theme.colors.white,
    fontWeight: "600",
  },
  noDataCard: {
    alignItems: "center",
    paddingVertical: theme.spacing.xl,
  },
  noDataText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: theme.spacing.md,
    lineHeight: 22,
  },
  facilityCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  facilityRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    flex: 1,
  },
  imageContainer: {
    position: "relative",
    marginRight: theme.spacing.md,
  },
  facilityImage: {
    width: 80,
    height: 80,
    borderRadius: theme.radii.lg,
    backgroundColor: theme.colors.gray200,
  },
  imageOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 30,
    borderBottomLeftRadius: theme.radii.lg,
    borderBottomRightRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
  },
  facilityInfo: {
    flex: 1,
  },
  headerRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: theme.spacing.xs,
  },
  facilityName: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
  },
  facilityDescription: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray700,
    lineHeight: 18,
    marginBottom: theme.spacing.sm,
  },
  scheduleRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: theme.spacing.xs,
  },
  scheduleText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: theme.spacing.xs,
    fontWeight: "500",
  },
  daysRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: theme.spacing.sm,
  },
  daysText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: theme.spacing.xs,
  },
  extraInfoRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: theme.spacing.xs,
  },
  featureTag: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: theme.colors.gray100,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
    marginRight: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  featureText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray700,
    marginLeft: 2,
    fontWeight: "500",
  },
});
