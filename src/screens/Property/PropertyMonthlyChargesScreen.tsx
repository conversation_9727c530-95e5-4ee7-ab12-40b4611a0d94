import React, { useState, useMemo } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { useRoute, RouteProp } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { GradientView } from "../../components/layouts/GradientView";
import { Card } from "../../components/main/Card";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { formatDateDMY } from "../../utils/date-time.utils";
import { Loading } from "../../components/Loading";
import { PropertyStackParamList } from "../../navigation/types";
import {
  formatCurrency,
  isChargeOverdue,
} from "../../components/Property/MonthlyChargesSection";
import { getChargeStatus } from "../../utils/convertions";

type PropertyMonthlyChargesRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyMonthlyCharges"
>;

export const PropertyMonthlyChargesScreen: React.FC = () => {
  const route = useRoute<PropertyMonthlyChargesRouteProp>();
  const { filterPaid } = route.params || {};

  const [selectedFilter, setSelectedFilter] = useState<
    boolean | "ALL" | "OVERDUE"
  >(filterPaid ?? "ALL");

  const { data: userData, isLoading } = useCachedQuery<Me>(`mobile/me`);

  // Mover todos los hooks antes del early return
  const properties = userData?.properties || [];
  const selectedProperty = properties[0]; // Por simplicidad, usar la primera propiedad
  const charges = selectedProperty?.monthlyMaintenanceCharges || [];

  // Filtrar cargos según el filtro seleccionado
  const filteredCharges = useMemo(() => {
    if (selectedFilter === "ALL") return charges;
    return charges.filter((charge) => charge.isPaid === selectedFilter);
  }, [charges, selectedFilter]);

  // Estadísticas
  const unpaidCharges = charges.filter((c) => !c.isPaid);
  const paidCharges = charges.filter((c) => c.isPaid);
  const overdueCharges = unpaidCharges.filter((c) =>
    isChargeOverdue(c.dueDate)
  );

  // Filtrar por vencidos si está seleccionado
  const finalFilteredCharges = useMemo(() => {
    if (selectedFilter === "OVERDUE") {
      return overdueCharges;
    }
    return filteredCharges;
  }, [filteredCharges, overdueCharges, selectedFilter]);

  if (isLoading) return <Loading />;
  if (!userData) return <Text>Sin datos</Text>;

  const filterOptions = [
    {
      key: "ALL",
      label: "Todos",
      count: charges.length,
      color: theme.colors.gray500,
    },
    {
      key: false,
      label: "Pendientes",
      count: unpaidCharges.length,
      color: theme.colors.warning,
    },
    {
      key: true,
      label: "Pagados",
      count: paidCharges.length,
      color: theme.colors.success,
    },
    {
      key: "OVERDUE",
      label: "Vencidos",
      count: overdueCharges.length,
      color: theme.colors.error,
    },
  ];

  const sortedCharges = [...finalFilteredCharges].sort(
    (a, b) => new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime()
  );

  return (
    <GradientView
      firstLineText="Cargos Mensuales"
      secondLineText={selectedProperty?.address ?? "Mi Propiedad"}
    >
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <Card style={styles.summaryCard}>
          <Row align="center" style={styles.summaryRow}>
            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter(false)}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="clock-outline"
                  size={24}
                  color={theme.colors.warning}
                />
                <Text style={styles.summaryNumber}>{unpaidCharges.length}</Text>
                <Text style={styles.summaryLabel}>Pendientes</Text>
              </TouchableOpacity>
            </Col>

            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter(true)}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="check-circle"
                  size={24}
                  color={theme.colors.success}
                />
                <Text style={styles.summaryNumber}>{paidCharges.length}</Text>
                <Text style={styles.summaryLabel}>Pagados</Text>
              </TouchableOpacity>
            </Col>

            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter("OVERDUE")}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="alert-circle"
                  size={24}
                  color={theme.colors.error}
                />
                <Text style={styles.summaryNumber}>
                  {overdueCharges.length}
                </Text>
                <Text style={styles.summaryLabel}>Vencidos</Text>
              </TouchableOpacity>
            </Col>

            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter("ALL")}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="view-list-outline"
                  size={24}
                  color={theme.colors.gray500}
                />
                <Text style={styles.summaryNumber}>{charges.length}</Text>
                <Text style={styles.summaryLabel}>Todos</Text>
              </TouchableOpacity>
            </Col>
          </Row>
        </Card>

        {/* Lista de cargos */}
        {finalFilteredCharges.length === 0 ? (
          <Card style={styles.noDataCard}>
            <Text style={styles.noDataText}>
              {selectedFilter === "ALL"
                ? "No hay cargos mensuales registrados"
                : `No hay cargos ${filterOptions
                    .find((f) => f.key === selectedFilter)
                    ?.label.toLowerCase()}`}
            </Text>
          </Card>
        ) : (
          sortedCharges.map((charge) => {
            const isOverdue = !charge.isPaid && isChargeOverdue(charge.dueDate);
            const amount = charge.maintenanceFee?.amount ?? 0;
            const lateFee = charge.lateFeeApplied
              ? charge.lateFeeAmount ?? 0
              : 0;
            const totalAmount = amount + lateFee;

            const chargeStatus = getChargeStatus(charge.isPaid, isOverdue);

            return (
              <TouchableOpacity
                key={charge.id}
                onPress={() => console.log("Ver detalle de cargo:", charge.id)}
                activeOpacity={0.7}
              >
                <Card
                  style={[styles.chargeCard, isOverdue && styles.overdueCard]}
                >
                  <Row align="flex-start">
                    <View
                      style={[
                        styles.chargeIndicator,
                        {
                          backgroundColor: `${chargeStatus.color}20`,
                        },
                      ]}
                    >
                      <MaterialCommunityIcons
                        name={chargeStatus.icon}
                        size={24}
                        color={chargeStatus.color}
                      />
                    </View>
                    <Col style={styles.chargeInfo}>
                      <Row align="center" style={styles.headerRow}>
                        <Text style={styles.chargeAmount}>
                          {formatCurrency(totalAmount)}
                        </Text>
                        <View
                          style={[
                            styles.statusChip,
                            {
                              backgroundColor: chargeStatus.color,
                            },
                          ]}
                        >
                          <Text style={styles.statusText}>
                            {chargeStatus.label}
                          </Text>
                        </View>
                      </Row>

                      <Text style={styles.chargePeriod}>
                        {charge.month}/{charge.year}
                      </Text>

                      <Row align="center" style={styles.dateRow}>
                        <MaterialCommunityIcons
                          name="calendar"
                          size={14}
                          color={theme.colors.gray500}
                        />
                        <Text
                          style={[
                            styles.dateText,
                            isOverdue && styles.overdueDateText,
                          ]}
                        >
                          Vence: {formatDateDMY(charge.dueDate)}
                        </Text>
                      </Row>

                      {charge.lateFeeApplied && !charge.waivedLateFee && (
                        <Text style={styles.lateFeeText}>
                          Incluye recargo por mora: {formatCurrency(lateFee)}
                        </Text>
                      )}

                      {charge.paidAt && (
                        <Text style={styles.paidAtText}>
                          Pagado el: {formatDateDMY(charge.paidAt)}
                        </Text>
                      )}
                    </Col>
                    <MaterialCommunityIcons
                      name="chevron-right"
                      size={theme.fontSizes.lg}
                      color={theme.colors.gray500}
                    />
                  </Row>
                </Card>
              </TouchableOpacity>
            );
          })
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
    marginBottom: theme.spacing.md,
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
    paddingVertical: theme.spacing.sm,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  summaryHeader: {
    alignItems: "center",
  },
  summaryInfo: {
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  summaryTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.warning,
  },
  summaryAmount: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.warning,
    marginTop: 2,
  },
  filtersContainer: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
  },
  filterButton: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.radii.pill,
    borderWidth: 2,
    marginRight: theme.spacing.sm,
    backgroundColor: theme.colors.white,
  },
  filterButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  filterButtonText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
  },
  filterButtonTextActive: {
    color: theme.colors.white,
  },
  noDataCard: {
    margin: theme.spacing.lg,
    padding: theme.spacing.xl,
    alignItems: "center",
  },
  noDataText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    textAlign: "center",
    fontStyle: "italic",
  },
  chargeCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  overdueCard: {
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.error,
    backgroundColor: `${theme.colors.error}05`,
  },
  chargeIndicator: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  chargeInfo: {
    flex: 1,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.sm,
  },
  chargeAmount: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
  },
  statusChip: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    textTransform: "uppercase",
  },
  chargePeriod: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.primary,
    marginBottom: theme.spacing.xs,
  },
  dateRow: {
    marginBottom: theme.spacing.xs,
  },
  dateText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  overdueDateText: {
    color: theme.colors.error,
    fontWeight: "600",
  },
  lateFeeText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.warning,
    fontStyle: "italic",
    marginBottom: theme.spacing.xs,
  },
  paidAtText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.success,
    fontStyle: "italic",
  },
});
