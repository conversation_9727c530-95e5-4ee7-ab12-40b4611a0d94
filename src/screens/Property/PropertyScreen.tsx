import { StyleSheet, Text } from "react-native";
import { useState } from "react";
import { GradientView } from "../../components/layouts/GradientView";
import { theme } from "../../theme/theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { ScrollView } from "react-native-gesture-handler";
import { PropertySelector } from "../../components/Property/PropertySelector";
import { VisitsSection } from "../../components/Sections/VisitsSection";
import { ComplaintsSection } from "../../components/Sections/ComplaintsSection";
import { MaintenanceIssueReportsSection } from "../../components/Sections/MaintenanceIssueReportsSection";

import { Loading } from "../../components/Loading";
import { PropertyInfoSection } from "../../components/Property/PropertyInfoSection";
import { TagsSection } from "../../components/Property/TagsSection";
import { MonthlyChargesSection } from "../../components/Property/MonthlyChargesSection";
import { FinesSection } from "../../components/Property/FinesSection";
import { InfractionsSection } from "../../components/Property/InfractionsSection";
import { ReservationsSection } from "../../components/Property/ReservationsSection";

export const PropertyScreen: React.FC = () => {
  const { data: userData, error, isLoading } = useCachedQuery<Me>(`mobile/me`);
  const [selectedPropertyId, setSelectedPropertyId] = useState<string | null>(
    null
  );

  if (isLoading) return <Loading />;
  if (error) return <Text>Error al cargar los datos</Text>;
  if (!userData) return <Text>Sin datos</Text>;

  const properties = userData.properties || [];

  // Si no hay propiedades
  if (properties.length === 0) {
    return (
      <GradientView firstLineText="Mi propiedad">
        <ScrollView>
          <Text style={styles.noDataText}>
            No tienes propiedades registradas
          </Text>
        </ScrollView>
      </GradientView>
    );
  }

  // Determinar la propiedad seleccionada
  const selectedProperty =
    properties.length === 1
      ? properties[0]
      : properties.find((p) => p.id === selectedPropertyId) || properties[0];

  // Si hay múltiples propiedades pero no se ha seleccionado ninguna, usar la primera
  if (properties.length > 1 && !selectedPropertyId) {
    setSelectedPropertyId(properties[0].id);
  }

  return (
    <GradientView firstLineText="Mi propiedad">
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Selector de propiedades para usuarios con múltiples propiedades */}
        {properties.length > 1 && (
          <PropertySelector
            properties={properties}
            selectedPropertyId={selectedPropertyId}
            onPropertySelect={setSelectedPropertyId}
          />
        )}

        {/* Información básica de la propiedad */}
        <PropertyInfoSection property={selectedProperty} />

        {/* Visitas recientes */}
        {selectedProperty.visits.length > 0 && (
          <VisitsSection visits={selectedProperty.visits} />
        )}

        {/* Reservaciones */}
        {selectedProperty.reservations.length > 0 && (
          <ReservationsSection
            reservations={selectedProperty.reservations}
            property={selectedProperty}
          />
        )}

        {selectedProperty.fines.length > 0 && (
          <FinesSection
            fines={selectedProperty.fines}
            property={selectedProperty}
          />
        )}

        {selectedProperty.infractions.length > 0 && (
          <InfractionsSection
            infractions={selectedProperty.infractions}
            property={selectedProperty}
          />
        )}

        {/* Quejas */}
        {selectedProperty.complaints.length > 0 && (
          <ComplaintsSection
            complaints={selectedProperty.complaints}
            property={selectedProperty}
          />
        )}

        {/* Reportes de mantenimiento */}
        {selectedProperty.maintenanceIssueReports.length > 0 && (
          <MaintenanceIssueReportsSection
            maintenanceIssueReports={selectedProperty.maintenanceIssueReports}
            property={selectedProperty}
          />
        )}

        {/* Tags */}
        {selectedProperty.tags.length > 0 && (
          <TagsSection tags={selectedProperty.tags} />
        )}

        {/* Cargos mensuales */}
        {selectedProperty.monthlyMaintenanceCharges.length > 0 && (
          <MonthlyChargesSection
            charges={selectedProperty.monthlyMaintenanceCharges}
          />
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  finesInfractionsContainer: {
    marginTop: theme.spacing.md,
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.lg,
    color: theme.colors.gray500,
    marginTop: theme.spacing.xl,
    padding: theme.spacing.lg,
  },
});
