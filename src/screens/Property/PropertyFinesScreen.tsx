import { useState, useMemo } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { GradientView } from "../../components/layouts/GradientView";
import { Card } from "../../components/main/Card";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { formatDateDMY } from "../../utils/date-time.utils";
import { Loading } from "../../components/Loading";
import { PropertyFinesRouteProp } from "../../navigation/types";
import { useRoute } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";

type FineStatus = "PAID" | "UNPAID" | "ALL";

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("es-MX", {
    style: "currency",
    currency: "MXN",
  }).format(amount);
};

const getNoDataMessage = (filter: FineStatus): string => {
  switch (filter) {
    case "ALL":
      return "No hay multas registradas";
    case "PAID":
      return "No hay multas pagadas";
    case "UNPAID":
      return "No hay multas pendientes";
    default:
      return "No hay multas registradas";
  }
};

export const PropertyFinesScreen: React.FC = () => {
  const route = useRoute<PropertyFinesRouteProp>();
  const { fines, property } = route.params || {};

  const [selectedFilter, setSelectedFilter] = useState<FineStatus>("UNPAID");

  const { data: userData, isLoading } = useCachedQuery<Me>(`mobile/me`);

  // Filtrar fines según el filtro seleccionado
  const filteredFines = useMemo(() => {
    if (selectedFilter === "ALL") return fines;
    if (selectedFilter === "PAID") return fines.filter((fine) => fine.isPaid);
    if (selectedFilter === "UNPAID")
      return fines.filter((fine) => !fine.isPaid);
    return fines;
  }, [fines, selectedFilter]);

  if (isLoading) return <Loading />;
  if (!userData) return <Text>Sin datos</Text>;

  // Estadísticas
  const paidFines = fines.filter((f) => f.isPaid);
  const unpaidFines = fines.filter((f) => !f.isPaid);
  const totalOwed = unpaidFines.reduce((sum, fine) => sum + fine.amount, 0);

  return (
    <GradientView
      firstLineText="Multas"
      secondLineText={property?.address ?? "Mi Propiedad"}
    >
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <Card style={styles.summaryCard}>
          <Row align="center" style={styles.summaryRow}>
            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter("UNPAID")}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="close-circle"
                  size={24}
                  color={theme.colors.error}
                />
                <Text style={styles.summaryNumber}>{unpaidFines.length}</Text>
                <Text style={styles.summaryLabel}>Pendientes</Text>
              </TouchableOpacity>
            </Col>

            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter("PAID")}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="check-circle"
                  size={24}
                  color={theme.colors.success}
                />
                <Text style={styles.summaryNumber}>{paidFines.length}</Text>
                <Text style={styles.summaryLabel}>Pagadas</Text>
              </TouchableOpacity>
            </Col>

            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter("ALL")}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="view-list-outline"
                  size={24}
                  color={theme.colors.gray500}
                />
                <Text style={styles.summaryNumber}>{fines.length}</Text>
                <Text style={styles.summaryLabel}>Todas</Text>
              </TouchableOpacity>
            </Col>
          </Row>

          {/* Total adeudado */}
          {totalOwed > 0 && (
            <View style={styles.totalOwedContainer}>
              <Text style={styles.totalOwedLabel}>Total adeudado:</Text>
              <Text style={styles.totalOwedAmount}>
                {formatCurrency(totalOwed)}
              </Text>
            </View>
          )}
        </Card>

        {/* Lista de fines */}
        {filteredFines.length === 0 ? (
          <Card style={styles.noDataCard}>
            <Text style={styles.noDataText}>
              {getNoDataMessage(selectedFilter)}
            </Text>
          </Card>
        ) : (
          filteredFines.map((fine) => (
            <TouchableOpacity
              key={fine.id}
              onPress={() => console.log("Ver detalle de multa:", fine.id)}
              activeOpacity={0.7}
            >
              <Card style={styles.fineCard}>
                <Row align="flex-start">
                  <View
                    style={[
                      styles.statusIndicator,
                      {
                        backgroundColor: fine.isPaid
                          ? `${theme.colors.success}20`
                          : `${theme.colors.error}20`,
                      },
                    ]}
                  >
                    <MaterialCommunityIcons
                      name={fine.isPaid ? "check-circle" : "alert-circle"}
                      size={24}
                      color={
                        fine.isPaid ? theme.colors.success : theme.colors.error
                      }
                    />
                  </View>
                  <Col style={styles.fineInfo}>
                    <Row align="center" style={styles.headerRow}>
                      <Text style={styles.fineAmount}>
                        {formatCurrency(fine.amount)}
                      </Text>
                      <View
                        style={[
                          styles.statusChip,
                          {
                            backgroundColor: fine.isPaid
                              ? theme.colors.success
                              : theme.colors.error,
                          },
                        ]}
                      >
                        <MaterialCommunityIcons
                          name={fine.isPaid ? "check" : "close"}
                          size={12}
                          color={theme.colors.white}
                        />
                        <Text style={styles.statusText}>
                          {fine.isPaid ? "Pagada" : "Pendiente"}
                        </Text>
                      </View>
                    </Row>
                    <Text style={styles.fineDescription} numberOfLines={3}>
                      {fine.description}
                    </Text>
                    <Row align="center" style={styles.dateRow}>
                      <MaterialCommunityIcons
                        name="calendar"
                        size={14}
                        color={theme.colors.gray500}
                      />
                      <Text style={styles.dateText}>
                        Emitida: {formatDateDMY(fine.issuedAt)}
                      </Text>
                      {!!fine.paidAt && (
                        <>
                          <MaterialCommunityIcons
                            name="check"
                            size={14}
                            color={theme.colors.success}
                            style={styles.paidIcon}
                          />
                          <Text style={styles.paidText}>
                            Pagada: {formatDateDMY(fine.paidAt)}
                          </Text>
                        </>
                      )}
                    </Row>
                  </Col>
                  <MaterialCommunityIcons
                    name="chevron-right"
                    size={theme.fontSizes.lg}
                    color={theme.colors.gray500}
                  />
                </Row>
              </Card>
            </TouchableOpacity>
          ))
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
    marginBottom: theme.spacing.md,
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
    paddingVertical: theme.spacing.sm,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  totalOwedContainer: {
    backgroundColor: `${theme.colors.error}10`,
    borderRadius: theme.radii.md,
    padding: theme.spacing.md,
    marginTop: theme.spacing.sm,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.error,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  totalOwedLabel: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.error,
    fontWeight: "600",
  },
  totalOwedAmount: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.error,
  },
  noDataCard: {
    margin: theme.spacing.lg,
    padding: theme.spacing.xl,
    alignItems: "center",
  },
  noDataText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    textAlign: "center",
    fontStyle: "italic",
  },
  fineCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  statusIndicator: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  fineInfo: {
    flex: 1,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.sm,
  },
  fineAmount: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
  },
  statusChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    marginLeft: 4,
    textTransform: "capitalize",
  },
  fineDescription: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.black,
    lineHeight: 18,
    marginBottom: theme.spacing.sm,
  },
  dateRow: {
    marginBottom: theme.spacing.xs,
  },
  dateText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  paidIcon: {
    marginLeft: theme.spacing.md,
  },
  paidText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.success,
    marginLeft: 6,
  },
});
