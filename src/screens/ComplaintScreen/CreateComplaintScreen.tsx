import React, { useState, useMemo } from "react";
import { View, StyleSheet, ScrollView, Alert, Text } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { z } from "zod";
import { useZodForm } from "../../hooks/useZodForm";
import { TextAreaFormField } from "../../components/forms/TextAreaFormField";
import { SelectField } from "../../components/forms/SelectField";
import { ImagePickerField } from "../../components/forms/ImagePickerField";
import { GradientView } from "../../components/layouts/GradientView";
import { Button } from "../../components";
import { theme } from "../../theme";
import { useComplaints } from "../../hooks/useComplaints";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { QUERIES } from "../../constants/queries";
import { Create<PERSON>omplaint, Priority } from "../../interfaces/complaint";
import { ComplaintConfirmModal } from "../../components/modals/ComplaintConfirmModal";
import { SuccessModal } from "../../components/modals/SuccessModal";
import { ErrorModal } from "../../components/modals/ErrorModal";
import { LoadingOverlay } from "../../components/LoadingOverlay";

const complaintSchema = z.object({
  complaintTypeId: z
    .string({ required_error: "Tipo de queja requerido" })
    .min(1, "Selecciona un tipo de queja"),
  detail: z
    .string({ required_error: "Descripción requerida" })
    .min(10, "Mínimo 10 caracteres"),
  priority: z.nativeEnum(Priority, {
    required_error: "Prioridad requerida",
  }),
  images: z.array(z.string()).optional().default([]),
});

type ComplaintFormValues = z.infer<typeof complaintSchema>;

interface ComplaintConfirmData {
  complaintTypeName: string;
  detail: string;
  priority: string;
  imagesCount: number;
}

export const CreateComplaintScreen: React.FC = () => {
  const navigation = useNavigation();
  const me = useCachedQuery<Me>(QUERIES.ME);
  const propertyId = me.data?.properties[0].id ?? "";

  // Estados para modales
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Hooks
  const { complaintTypes, createComplaint } = useComplaints();

  const { control, handleSubmit, watch } = useZodForm(complaintSchema, {
    defaultValues: {
      complaintTypeId: "",
      detail: "",
      priority: Priority.MEDIUM,
      images: [],
    },
  });

  // Observar valores del formulario para el modal de confirmación
  const watchedValues = watch();

  // Opciones para los selects
  const complaintTypeOptions = useMemo(() => {
    return (
      complaintTypes.data?.map((type) => ({
        label: type.name,
        value: type.id,
      })) || []
    );
  }, [complaintTypes.data]);

  const priorityOptions = [
    { label: "Baja", value: Priority.LOW },
    { label: "Media", value: Priority.MEDIUM },
    { label: "Alta", value: Priority.HIGH },
  ];

  const onSubmit = (data: ComplaintFormValues) => {
    // Validación adicional
    if (!propertyId) {
      Alert.alert("Error", "No se pudo obtener la información de la propiedad");
      return;
    }

    // Mostrar modal de confirmación
    setShowConfirmModal(true);
  };

  // Función para confirmar la queja
  const handleConfirmComplaint = async () => {
    if (!propertyId) return;

    try {
      const complaintData: CreateComplaint = {
        propertyId,
        complaintTypeId: watchedValues.complaintTypeId,
        detail: watchedValues.detail,
        priority: watchedValues.priority,
      };

      await createComplaint.mutateAsync(complaintData);

      // Cerrar modal de confirmación y mostrar éxito
      setShowConfirmModal(false);
      setShowSuccessModal(true);
    } catch (error: any) {
      // Cerrar modal de confirmación y mostrar error
      setShowConfirmModal(false);
      setErrorMessage(
        error?.response?.data?.message ?? "Error al crear la queja"
      );
      setShowErrorModal(true);
    }
  };

  // Función para cerrar modal de éxito y navegar de vuelta
  const handleSuccessClose = () => {
    setShowSuccessModal(false);
    navigation.goBack();
  };

  // Función para cerrar modal de error
  const handleErrorClose = () => {
    setShowErrorModal(false);
  };

  // Preparar datos para el modal de confirmación
  const confirmData: ComplaintConfirmData | null = useMemo(() => {
    const selectedType = complaintTypes.data?.find(
      (type) => type.id === watchedValues.complaintTypeId
    );

    if (!selectedType) return null;

    const priorityLabel =
      priorityOptions.find((option) => option.value === watchedValues.priority)
        ?.label ?? "";

    return {
      complaintTypeName: selectedType.name,
      detail: watchedValues.detail,
      priority: priorityLabel,
      imagesCount: watchedValues.images?.length || 0,
    };
  }, [watchedValues, complaintTypes.data]);

  return (
    <GradientView firstLineText="Nueva Queja">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.form}>
          <SelectField
            name="complaintTypeId"
            control={control}
            label="Tipo de queja"
            placeholder="Selecciona el tipo de queja"
            options={complaintTypeOptions}
            disabled={complaintTypes.isLoading}
          />

          <SelectField
            name="priority"
            control={control}
            label="Prioridad"
            placeholder="Selecciona la prioridad"
            options={priorityOptions}
          />

          <TextAreaFormField
            name="detail"
            control={control}
            placeholder="Describe detalladamente tu queja..."
            numberOfLines={6}
          />

          <ImagePickerField
            name="images"
            control={control}
            label="Imágenes (opcional)"
            maxImages={5}
          />

          <Button
            title="Enviar Queja"
            onPress={handleSubmit(onSubmit)}
            disabled={createComplaint.isPending}
          />
          <Text style={styles.message}>
            Este formulario sirve para reportar un problema o inconformidad.
          </Text>
        </View>
      </ScrollView>

      {/* Loading Overlay */}
      <LoadingOverlay visible={createComplaint.isPending} />

      {/* Modal de Confirmación */}
      {confirmData && (
        <ComplaintConfirmModal
          visible={showConfirmModal}
          complaintData={{
            detail: confirmData.detail,
            priority: confirmData.priority,
            imagesCount: confirmData.imagesCount,
          }}
          onConfirm={handleConfirmComplaint}
          onCancel={() => setShowConfirmModal(false)}
          isLoading={createComplaint.isPending}
          title="Confirmar Queja"
        />
      )}

      {/* Modal de Éxito */}
      <SuccessModal
        visible={showSuccessModal}
        title="¡Queja Enviada!"
        message="Tu queja ha sido registrada exitosamente. Recibirás una respuesta pronto."
        onClose={handleSuccessClose}
        buttonText="Continuar"
      />

      {/* Modal de Error */}
      <ErrorModal
        visible={showErrorModal}
        title="Error al Enviar Queja"
        message={errorMessage}
        onClose={handleErrorClose}
        buttonText="Intentar de nuevo"
      />
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  form: {
    padding: theme.spacing.lg,
    gap: theme.spacing.md,
  },
  message: {
    color: theme.colors.gray500,
    fontSize: theme.fontSizes.sm,
    paddingHorizontal: theme.spacing.xl,
    textAlign: "center",
  },
});
