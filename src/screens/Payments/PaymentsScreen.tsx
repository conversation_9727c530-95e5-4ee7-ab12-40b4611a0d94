import { StyleSheet } from "react-native";
import { WebView } from "react-native-webview";
import { theme } from "../../theme";
import { Loading } from "../../components/Loading";
import { GradientView } from "../../components/layouts/GradientView";

export const PaymentsScreen: React.FC = () => {
  return (
    <GradientView firstLineText="Be resident">
      <WebView
        source={{ uri: "https://app.beresident.mx" }}
        style={styles.webview}
        onLoadStart={() => console.log("loading...")}
        onLoadEnd={() => console.log("loaded")}
        onError={() => console.log("error")}
        onNavigationStateChange={(navState) => {
          console.log("navigation state changed", navState);
        }}
        renderLoading={() => <Loading />}
        startInLoadingState={true}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        allowsBackForwardNavigationGestures={true}
        scalesPageToFit={true}
        bounces={false}
      />
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  
  webview: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: theme.colors.white,
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: theme.colors.white,
    paddingHorizontal: theme.spacing.xl,
  },
  errorTitle: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "600",
    color: theme.colors.black,
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.sm,
  },
  errorMessage: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    textAlign: "center",
    lineHeight: 22,
    marginBottom: theme.spacing.xl,
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.radii.lg,
  },
  retryButtonText: {
    color: theme.colors.white,
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
  },
});
